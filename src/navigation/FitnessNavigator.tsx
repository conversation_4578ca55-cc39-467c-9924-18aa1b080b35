import messaging from '@react-native-firebase/messaging';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  createStackNavigator,
  StackNavigationProp,
} from '@react-navigation/stack';
import React, { useEffect } from 'react';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

import { RootFitnessStackParamList } from '../../@types/navigation';
import FitnessTabBar from '../components/TabNavigator/FitnessTabBar';

import { RouteProp, useNavigation } from '@react-navigation/native';
import FitnessHeader from '../components/Header/fitnessHeader';
import CameraScreen from '../screens/fitness/camera';
import Chat from '../screens/fitness/chat';
import MainScreen from '../screens/fitness/mainscreen';
import Mealdata from '../screens/fitness/mealdata';
import Profile from '../screens/fitness/profile';
import { LightTheme } from '../theme/colors';

const Stack = createStackNavigator<RootFitnessStackParamList>();
const Tab = createBottomTabNavigator<RootFitnessStackParamList>();
function StackNavigator() {
  const theme = LightTheme;
  const navigation = useNavigation();
  return (
    <Stack.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        header: ({ navigation, route }) => (
          <FitnessHeader
            navigation={
              navigation as StackNavigationProp<RootFitnessStackParamList>
            }
            route={route as RouteProp<RootFitnessStackParamList>}
          />
        ),
      }}
    >
      <Stack.Screen
        name="Home"
        component={MainScreen}
        options={{
          headerShown: true,
        }}
        initialParams={{ theme, navigation }}
      />

      <Stack.Screen
        name="Coach"
        component={Chat}
        initialParams={{ theme, navigation }}
      />

      <Stack.Screen
        name="Profile"
        component={Profile}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Capture"
        component={CameraScreen}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Mealdata"
        component={Mealdata}
        initialParams={{ theme, navigation }}
      />
    </Stack.Navigator>
  );
}

export default function FitnessNavigator() {
  useEffect(() => {
    const requestNotificationPermission = async () => {
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            console.log('Notification permission granted on Android 13+');
          } else {
            Alert.alert(
              'Permission required',
              'Enable notifications in your settings to receive updates.',
            );
          }
        } catch (error) {
          console.warn('Notification permission request error:', error);
        }
      }
    };

    requestNotificationPermission();

    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      if (remoteMessage?.notification) {
        Alert.alert(
          remoteMessage.notification.title ?? 'Notification',
          remoteMessage.notification.body ?? '',
        );
      }
    });

    return unsubscribe;
  }, []);
  return (
    <Tab.Navigator
      tabBar={(props) => <FitnessTabBar {...props} />}
      initialRouteName="StackNavigator"
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
      }}
    >
      <Tab.Screen name="StackNavigator" component={StackNavigator} />
    </Tab.Navigator>
  );
}
