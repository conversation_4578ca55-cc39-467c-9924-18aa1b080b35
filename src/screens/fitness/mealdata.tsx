import { StackScreenProps } from '@react-navigation/stack';
import React, { useEffect } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { FitnessRootStackParamList } from '../../../@types/fitnessnavigator';
import Header from '../../components/Header/Header';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';

const Mealdata: React.FC<
  StackScreenProps<FitnessRootStackParamList, 'Mealdata'>
> = ({ route }) => {
  const result = useSelector((state: RootState) => state.app.analyzeFoodResult);
  const { photoUri } = route.params;

  useEffect(() => {
    if (result) {
      console.log(result);
    }
  }, [result]);

  return (
    <>
      <Header
        headerTitle={result ? 'Review Meal' : 'Scan Meal'}
        showDivider={false}
        textStyle={{ textAlign: 'center', flex: 1 }}
      />
      <View style={styles.container}>
        <Image source={{ uri: photoUri }} style={styles.image} />
        <Text style={fontStyles?.Maison_600_20PX_28LH}>Scanning Meal...</Text>
        <View style={{ marginTop: 5 }}>
          <Text style={fontStyles?.Maison_500_14PX_18LH}>
            Analyzing your meal with AI...
          </Text>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  image: {
    width: 250,
    height: 250,
    borderRadius: 130,
    marginBottom: 26,
  },
  text: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 12,
  },
});

export default Mealdata;
