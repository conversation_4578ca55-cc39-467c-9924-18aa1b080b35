import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Button,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import LiveDataForm from './LiveDataForm';
import { supabase } from './supabaseClient';

const Profile = () => {
  const navigation = useNavigation();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);
  // Modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [fieldToEdit, setFieldToEdit] = useState(null); // 'calories' or 'steps'
  const [inputValue, setInputValue] = useState('');
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        console.log('info', 'Starting to fetch user data', 'Profile');

        const {
          data: { user },
          error: userError,
        } = await supabase.auth.getUser();
        if (userError) {
          console.error('Error getting authenticated user:', userError);
          console.log(
            'error',
            `Error getting authenticated user: ${userError.message}`,
            'Profile',
          );
          return;
        }
        if (!user) {
          console.log('No authenticated user found');
          console.log('warn', 'No authenticated user found', 'Profile');
          return;
        }

        console.log(
          'info',
          `Authenticated user found: ${JSON.stringify(user)}`,
          'Profile',
        );

        const { data, error } = await supabase
          .from('user_fitness_profiles')
          .select('id,user_id, daily_goals_calories_burned, daily_goals_steps')
          .eq('user_id', user.id)
          .single();
        if (error) {
          console.error('Error fetching user data:', error, user.id);
          console.log(
            'error',
            `Error fetching fitness profile: ${error.message}`,
            'Profile',
          );
          return;
        }

        console.log(
          'info',
          `User fitness data loaded - Calories: ${data.daily_goals_calories_burned}, Steps: ${data.daily_goals_steps}`,
          'Profile',
        );
        setUserData(data);
      } catch (err) {
        console.error('Unexpected error:', err);
        console.log(
          'error',
          `Unexpected error in fetchUserData: ${JSON.stringify(err)}`,
          'Profile',
        );
      }
    };
    fetchUserData();
  }, []);
  const openEditModal = (field) => {
    console.log(`[Profile] Opening edit modal for field: ${field}`);
    console.log('info', `Opening edit modal for field: ${field}`, 'Profile');

    setFieldToEdit(field);
    if (field === 'calories') {
      const currentValue =
        userData.daily_goals_calories_burned?.toString() || '';
      setInputValue(currentValue);
      console.log(`[Profile] Current calories value: ${currentValue}`);
      console.log(
        'debug',
        `Current calories value: ${currentValue}`,
        'Profile',
      );
    } else if (field === 'steps') {
      const currentValue = userData.daily_goals_steps?.toString() || '';
      setInputValue(currentValue);
      console.log(`[Profile] Current steps value: ${currentValue}`);
      console.log('debug', `Current steps value: ${currentValue}`, 'Profile');
    }
    setModalVisible(true);
  };
  const handleSave = async () => {
    console.log(
      'info',
      `Attempting to save ${fieldToEdit} with value: ${inputValue}`,
      'Profile',
    );

    if (!userData || !fieldToEdit) {
      console.log(
        'warn',
        'Cannot save: missing userData or fieldToEdit',
        'Profile',
      );
      return;
    }

    const numValue = parseInt(inputValue, 10);
    if (isNaN(numValue)) {
      Alert.alert('Invalid Input', 'Please enter a valid number.');
      console.log('error', `Invalid input value: ${inputValue}`, 'Profile');
      return;
    }
    setLoading(true);
    const updates =
      fieldToEdit === 'calories'
        ? { daily_goals_calories_burned: numValue }
        : { daily_goals_steps: numValue };
    try {
      console.log(
        'info',
        `Sending update to database: ${JSON.stringify(updates)}`,
        'Profile',
      );
      const { error } = await supabase
        .from('user_fitness_profiles')
        .update(updates)
        .eq('user_id', userData.user_id);
      if (error) {
        console.log(
          'error',
          `Database update failed: ${error.message}`,
          'Profile',
        );
        Alert.alert('Update Failed', error.message);
      } else {
        console.log(
          'info',
          `Successfully updated ${fieldToEdit} to ${numValue}`,
          'Profile',
        );
        setUserData((prev) => ({ ...prev, ...updates }));
        Alert.alert('Success', 'Goal updated successfully!');
        setModalVisible(false);
      }
    } catch (err) {
      console.log(
        'error',
        `Unexpected error during save: ${JSON.stringify(err)}`,
        'Profile',
      );
      Alert.alert('Error', 'Unexpected error occurred.');
      console.error(err);
    } finally {
      setLoading(false);
      console.log('info', 'Save operation completed', 'Profile');
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut(); // Supabase sign out
      await GoogleSignin.revokeAccess(); // Revoke Google access
      await GoogleSignin.signOut(); // Google sign out
      navigation.navigate('Login_', { tokenRefresher: 'tokenRefresh' });
      Alert.alert('Signed Out', 'You have been signed out.');
      // Optionally navigate back to Login screen if needed
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  };

  if (!userData) {
    return (
      <View style={styles.container}>
        <Text style={{ color: 'white' }}>Loading user goals...</Text>
      </View>
    );
  }
  return (
    <View style={styles.container}>
      <View style={{ marginTop: 30 }}>
        <Button title="Sign Out" onPress={signOut} />
      </View>
      <Text style={styles.header}>Daily Goals</Text>
      <View style={styles.grid}>
        {/* Calories Box */}
        <View style={styles.box}>
          <Text style={styles.label}>Calories</Text>
          <View style={styles.valueRow}>
            <Text style={styles.value}>
              {userData.daily_goals_calories_burned}
            </Text>
            <TouchableOpacity
              onPress={() => openEditModal('calories')}
              style={styles.editIconContainer}
            >
              <FontAwesome name="edit" size={28} color={'black'} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Steps Box */}
        <View style={styles.box}>
          <Text style={styles.label}>Steps</Text>
          <View style={styles.valueRow}>
            <Text style={styles.value}>{userData.daily_goals_steps}</Text>
            <TouchableOpacity
              onPress={() => openEditModal('steps')}
              style={styles.editIconContainer}
            >
              <FontAwesome name="edit" size={28} color={'black'} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Live Health Form (LiveDataForm) */}
      <Text style={[styles.header, { marginTop: 30 }]}>
        Notification Dry Run
      </Text>
      <LiveDataForm />

      {/* Edit Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalBackground}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>
              Edit {fieldToEdit === 'calories' ? 'Calories' : 'Steps'}
            </Text>
            <TextInput
              style={styles.modalInput}
              keyboardType="numeric"
              value={inputValue}
              onChangeText={setInputValue}
              editable={!loading}
              placeholder="Enter new value"
              autoFocus
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={[styles.modalButton, styles.cancelButton]}
                disabled={loading}
              >
                <Text style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleSave}
                style={[
                  styles.modalButton,
                  loading ? styles.buttonDisabled : null,
                ]}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.modalButtonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};
export default Profile;
const styles = StyleSheet.create({
  box: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    flex: 1,
    marginHorizontal: 10,
    padding: 20,
  },
  buttonDisabled: { backgroundColor: '#A0A0A0' },
  cancelButton: { backgroundColor: '#ccc' },
  cancelText: { color: '#333', fontSize: 16, fontWeight: '600' },
  container: {
    flex: 1,
    // alignItems: 'center',
    padding: 10,
    backgroundColor: 'black',
  },
  editIcon: { fontSize: 20 },
  editIconContainer: { marginLeft: 10 },
  grid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  header: {
    color: '#fff',
    fontSize: 24,
    marginBottom: 10,
    textAlign: 'center',
  },
  label: { color: '#333', fontSize: 16, marginBottom: 10 },
  modalBackground: {
    flex: 1,
    //   backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    padding: 29,
  },
  modalButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    marginLeft: 10,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  modalButtonText: { color: '#fff', fontSize: 16, fontWeight: '600' },
  modalButtons: { flexDirection: 'row', justifyContent: 'flex-end' },
  modalContainer: { backgroundColor: '#fff', borderRadius: 12, padding: 20 },
  modalInput: {
    borderColor: '#999',
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 18,
    marginBottom: 20,
    padding: 10,
  },
  modalTitle: { fontSize: 18, fontWeight: '600', marginBottom: 15 },
  value: { color: '#000', fontSize: 28, fontWeight: 'bold' },
  valueRow: { alignItems: 'center', flexDirection: 'row' },
});
