// MainScreen.js
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  AppState,
  AppStateStatus,
  NativeModules,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import BrokenHealthKit, { HealthKitPermissions } from 'react-native-health';

import {
  initialize,
  RecordResult,
  requestPermission,
} from 'react-native-health-connect';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';

import { FitnessScreenProps } from '../../../@types/navigation';
import {
  InfoIcon,
  KcaConsumedIcon1,
  KcaConsumedIcon2,
  KcaConsumedIcon3,
  LeftArrow,
} from '../../assets/images';
import { CommonCard } from '../../components/Card/Card';
import EnergyMeter from '../../components/FitnessComponent/Cards/EnergyMeter';
import MotivationCard from '../../components/FitnessComponent/Cards/MotivationCard';
import SyncCard from '../../components/FitnessComponent/Cards/syncCard';
import { useFetchSleepData } from '../../components/FitnessComponent/hooks/fetchSleepData';
import { useFetchStepData } from '../../components/FitnessComponent/hooks/fetchStepData';
import { useFetchWeightData } from '../../components/FitnessComponent/hooks/fetchWeightData';
import { useSleepSync } from '../../components/FitnessComponent/hooks/useSleepSync';
import { useStepSync } from '../../components/FitnessComponent/hooks/useStepSync';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import { useWeightSync } from '../../components/FitnessComponent/hooks/useWeightSync';
import CircularProgress from '../../components/FitnessComponent/progressCards/circularProgress';
import { fontStyles } from '../../theme/fonts';
import { formatMinutesToHours } from '../../utils/fitnessFunctions';
import { fetchLastSyncedAt } from './supabaseClient';

interface FitnessData {
  steps: number;
  distance: number;
  calories: { active: number; total: number };
  heartRate: any[]; // you can type better if you want
  sleep: any[];
  activities: any[];
  weight: RecordResult<'Weight'>[]; // <-- explicitly declare this type
  bloodPressure: any[];
  bloodGlucose: any[];
}

const MainScreen: React.FC<FitnessScreenProps<'Home'>> = ({
  navigation,
  route,
}) => {
  let AppleHealthKit: typeof BrokenHealthKit | undefined;

  if (Platform.OS === 'ios') {
    AppleHealthKit = NativeModules.AppleHealthKit as typeof BrokenHealthKit;
    AppleHealthKit.Constants = BrokenHealthKit.Constants;
  }
  const { theme } = route.params;
  const colors = theme?.colors;
  const [authorized, setAuthorized] = useState(false);
  const [startDate, setStartDate] = useState<any>(null);
  const [endDate, setEndDate] = useState(new Date());
  const [sleepStartDate, setSleepStartDate] = useState<Date | null>(null);
  const [sleepEndDate, setSleepEndDate] = useState(new Date());

  const { loading: userFetchLoading, userId } = useUserId();
  // const [loading, setLoading] = useState(true);
  const [loadingSteps, setLoadingSteps] = useState(false);
  const [loadingWeight, setLoadingWeight] = useState(false);
  const [loadingSleep, setLoadingSleep] = useState(false);
  const appState = useRef<AppStateStatus>(AppState.currentState);
  // Combined loading if you want a single flag
  const loading = loadingSteps || loadingWeight || loadingSleep;

  const [fitnessData, setFitnessData] = useState<FitnessData>({
    steps: 0,
    distance: 0,
    calories: { active: 0, total: 0 },
    heartRate: [],
    sleep: [],
    activities: [],
    weight: [],
    bloodPressure: [],
    bloodGlucose: [],
  });

  const [stepsFromDb, setStepsFromDb] = useState<any>(null);
  const [weightFromDb, setWeightFromDb] = useState<any>(null);
  const [sleepFromDb, setSleepFromDb] = useState<any>(null);
  const [weightStartDate, setWeightStartDate] = useState<Date | null>(null);
  const [weightEndDate, setWeightEndDate] = useState(new Date());
  const [isSyncing, setIsSyncing] = useState<boolean | null>(null);
  const [warningSync, setWarningSync] = useState<boolean | null>(false);

  const stepSync = useStepSync(setStepsFromDb, userId);
  const weightSync = useWeightSync(setWeightFromDb, userId);
  const sleepSync = useSleepSync(setSleepFromDb, userId);

  const fetchStepData = useFetchStepData(setFitnessData, setStepsFromDb);
  const fetchWeightData = useFetchWeightData(setFitnessData, setWeightFromDb);
  const fetchSleepData = useFetchSleepData(setFitnessData, setSleepFromDb);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async (nextAppState) => {
        if (
          appState.current.match(/inactive|background/) &&
          nextAppState === 'active'
        ) {
          console.log(
            '[AppState] App has come to the foreground - calling sleepSync',
          );
          console.log(
            'info',
            'App came to foreground, starting data sync - sleepsync',
            'MainScreen',
          );

          if (authorized && userId) {
            try {
              setLoadingSleep(true);
              // await stepSync();

              // await weightSync();
              // await sleepSync();
            } catch (e) {
              let errorMessage = 'Unknown error';

              if (e && typeof e === 'object' && 'message' in e) {
                errorMessage = String((e as { message?: unknown }).message);
              } else if (typeof e === 'string') {
                errorMessage = e;
              }
              console.error('[AppState] Error calling sleepSync on focus:', e);
              console.log(
                'error',
                `App state sync failed: ${errorMessage}`,
                'MainScreen',
              );
            } finally {
              setLoadingSleep(false);
              console.log('info', 'App state sync completed', 'MainScreen');
            }
          }
        }
        appState.current = nextAppState;
      },
    );

    return () => subscription.remove();
  }, [authorized, userId, sleepSync, stepSync, weightSync]);

  // Setup Health Connect permissions & initialization
  const setupHealthConnect = useCallback(
    async (loginUserId: string) => {
      try {
        if (userFetchLoading || !loginUserId) {
          return;
        }
        console.log('[setupHealthConnect] Initializing Health Connect...');
        console.log(
          'info',
          'Starting Health Connect initialization',
          'MainScreen',
        );

        const isAvailable = await initialize();
        if (!isAvailable) {
          console.warn(
            '[setupHealthConnect] Health Connect not available on this device',
          );
          Alert.alert('Health Connect not available on this device');
          return;
        }

        await requestPermission([
          { accessType: 'read', recordType: 'Steps' },
          { accessType: 'read', recordType: 'Distance' },
          { accessType: 'read', recordType: 'HeartRate' },
          { accessType: 'read', recordType: 'SleepSession' },
          { accessType: 'read', recordType: 'ActiveCaloriesBurned' },
          { accessType: 'read', recordType: 'TotalCaloriesBurned' },
          { accessType: 'read', recordType: 'Weight' },
          { accessType: 'read', recordType: 'BloodPressure' },
          { accessType: 'read', recordType: 'BloodGlucose' },
        ]);
        console.log('[setupHealthConnect] Permissions granted.');
        console.log(
          'info',
          'Health Connect permissions granted successfully',
          'MainScreen',
        );

        console.log('[setupHealthConnect] User ID:', loginUserId);
        console.log(
          'info',
          `Health Connect setup for user: ${loginUserId}`,
          'MainScreen',
        );
        const lastSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'steps',
        );
        console.log('[setupHealthConnect] Last synced at:', lastSyncedStr);
        const lastSynced = lastSyncedStr ? new Date(lastSyncedStr) : null;
        if (!lastSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setStartDate(todayStart);
          console.log(
            '[setupHealthConnect] No last sync found, setting startDate:',
            todayStart,
          );
        } else {
          setStartDate(null);
          console.log(
            '[setupHealthConnect] Last sync exists, startDate set to null',
          );
        }
        setEndDate(new Date());

        const lastWeightSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'weight',
        );
        const lastWeightSynced = lastWeightSyncedStr
          ? new Date(lastWeightSyncedStr)
          : null;

        if (!lastWeightSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setWeightStartDate(todayStart);
        } else {
          setWeightStartDate(null);
        }
        setWeightEndDate(new Date());

        const lastSleepSyncedStr = await fetchLastSyncedAt(
          loginUserId as string,
          'sleep',
        );
        const lastSleepSynced = lastSleepSyncedStr
          ? new Date(lastSleepSyncedStr)
          : null;

        if (!lastSleepSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setSleepStartDate(todayStart);
        } else {
          setSleepStartDate(null);
        }
        setSleepEndDate(new Date());

        setAuthorized(true);
        console.log('[setupHealthConnect] Authorized set to true.');
        console.log(
          'info',
          'Health Connect authorization completed',
          'MainScreen',
        );
      } catch (e) {
        let errorMessage = 'Unknown error';

        if (e && typeof e === 'object' && 'message' in e) {
          errorMessage = String((e as { message?: unknown }).message);
        } else if (typeof e === 'string') {
          errorMessage = e;
        }

        console.error('[setupHealthConnect] Error:', e);
        console.log(
          'error',
          `Health Connect setup failed: ${errorMessage}`,
          'MainScreen',
        );
        Alert.alert('Health Connect Setup Error', 'Failed to connect');
      }
    },
    [userFetchLoading],
  );

  const useHealthKitSetup = useCallback(
    async (loginUserId: string) => {
      if (Platform.OS !== 'ios') return;

      try {
        if (userFetchLoading || !loginUserId) {
          return;
        }

        console.log('[setupHealthKit] Initializing HealthKit...');

        const permissions: HealthKitPermissions = {
          permissions: {
            read: [
              AppleHealthKit.Constants.Permissions.StepCount,
              AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
              AppleHealthKit.Constants.Permissions.HeartRate,
              AppleHealthKit.Constants.Permissions.SleepAnalysis,
              AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
              AppleHealthKit.Constants.Permissions.BasalEnergyBurned,
              AppleHealthKit.Constants.Permissions.Weight,
              AppleHealthKit.Constants.Permissions.BloodPressureSystolic,
              AppleHealthKit.Constants.Permissions.BloodPressureDiastolic,
              AppleHealthKit.Constants.Permissions.BloodGlucose,
            ],
            write: [],
          },
        };

        await new Promise<void>((resolve, reject) => {
          AppleHealthKit.initHealthKit(permissions, (error) => {
            if (error) {
              console.error('[setupHealthKit] Init error:', error);
              Alert.alert('Apple Health Setup Error', 'Failed to connect');
              reject(error);
            } else {
              console.log('[setupHealthKit] HealthKit permissions granted.');
              resolve();
            }
          });
        });

        console.log('[setupHealthKit] User ID:', loginUserId);

        const lastSyncedStr = await fetchLastSyncedAt(loginUserId, 'steps');
        const lastSynced = lastSyncedStr ? new Date(lastSyncedStr) : null;

        if (!lastSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setStartDate(todayStart);
          console.log(
            '[setupHealthKit] No last sync, setting startDate:',
            todayStart,
          );
        } else {
          setStartDate(null);
        }
        setEndDate(new Date());

        const lastWeightSyncedStr = await fetchLastSyncedAt(
          loginUserId,
          'weight',
        );
        const lastWeightSynced = lastWeightSyncedStr
          ? new Date(lastWeightSyncedStr)
          : null;

        if (!lastWeightSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setWeightStartDate(todayStart);
        } else {
          setWeightStartDate(null);
        }
        setWeightEndDate(new Date());

        const lastSleepSyncedStr = await fetchLastSyncedAt(
          loginUserId,
          'sleep',
        );
        const lastSleepSynced = lastSleepSyncedStr
          ? new Date(lastSleepSyncedStr)
          : null;

        if (!lastSleepSynced) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0);
          setSleepStartDate(todayStart);
        } else {
          setSleepStartDate(null);
        }
        setSleepEndDate(new Date());

        setAuthorized(true);
        console.log('[setupHealthKit] Authorized set to true.');
      } catch (e) {
        let errorMessage = 'Unknown error';
        if (e && typeof e === 'object' && 'message' in e) {
          errorMessage = String((e as { message?: unknown }).message);
        } else if (typeof e === 'string') {
          errorMessage = e;
        }

        console.error('[setupHealthKit] Error:', e);
        Alert.alert('Apple Health Setup Error', errorMessage);
      }
    },
    [userFetchLoading],
  );

  // On mount: setup Health Connect on Android
  useEffect(() => {
    if (Platform.OS === 'android' && !userFetchLoading) {
      console.log(
        '[useEffect] Platform is Android, starting setupHealthConnect...',
        userFetchLoading,
        userId,
      );
      setupHealthConnect(userId as string);
    }
  }, [userId, setupHealthConnect, userFetchLoading]);

  // On mount: setup Health Connect on Android
  useEffect(() => {
    if (Platform.OS === 'ios' && !userFetchLoading) {
      console.log(
        '[useEffect] Platform is Ios, starting useHealthKitSetup...',
        userFetchLoading,
        userId,
      );
      useHealthKitSetup(userId as string);
    }
  }, [userId, useHealthKitSetup, userFetchLoading]);

  // Steps useEffect
  useEffect(() => {
    if (!authorized || !userId) {
      return;
    }
    console.log('1');

    (async () => {
      setLoadingSteps(true);
      try {
        // const lastStepsSync = await fetchLastSyncedAt(userId, 'steps');
        // console.log(lastStepsSync,'check208');
        // if (!lastStepsSync) {
        //   await fetchStepData(startDate, endDate);
        // } else {
        // await stepSync();
        // lastStepsSync
        // }
      } catch (e) {
        console.error('[Steps] Error during sync:', e);
      } finally {
        setLoadingSteps(false);
      }
    })();
  }, [authorized, userId, startDate, endDate, fetchStepData, stepSync]);

  // Weight useEffect
  useEffect(() => {
    if (!authorized || !userId) {
      return;
    }
    console.log('2');
    (async () => {
      setLoadingWeight(true);
      try {
        // const lastWeightSync = await fetchLastSyncedAt(userId, 'weight');
        // if (!lastWeightSync) {
        //   await fetchWeightData(weightStartDate, weightEndDate);
        // } else {
        // await weightSync();
        // lastWeightSync
        // }
      } catch (e) {
        console.error('[Weight] Error during sync:', e);
      } finally {
        setLoadingWeight(false);
      }
    })();
  }, [
    authorized,
    userId,
    weightStartDate,
    weightEndDate,
    fetchWeightData,
    weightSync,
  ]);

  // Sleep useEffect
  useEffect(() => {
    if (!authorized || !userId) {
      return;
    }
    console.log('3');
    (async () => {
      setLoadingSleep(true);
      try {
        // const lastSleepSync = await fetchLastSyncedAt(userId, 'sleep');
        // if (!lastSleepSync) {
        //   await fetchSleepData(sleepStartDate, sleepEndDate);
        // } else {
        // await sleepSync();
        // lastSleepSync
        // }
      } catch (e) {
        console.error('[Sleep] Error during sync:', e);
      } finally {
        setLoadingSleep(false);
      }
    })();
  }, [
    authorized,
    userId,
    sleepStartDate,
    sleepEndDate,
    fetchSleepData,
    sleepSync,
  ]);

  const stepsToDisplay = stepsFromDb !== null ? stepsFromDb : fitnessData.steps;
  const weightToDisplay =
    weightFromDb !== null
      ? weightFromDb > 0
        ? `${weightFromDb} kg`
        : 0
      : fitnessData.weight.length;
  const sleepToDisplay =
    sleepFromDb !== null
      ? sleepFromDb > 0
        ? `${formatMinutesToHours(sleepFromDb)}`
        : 0
      : fitnessData.sleep.length;

  const metrics = [
    {
      label: 'Steps',
      value: stepsToDisplay,
      icon: 'walk',
      description: '1.2 km • 12:30 min • 7:00 AM',
    },
    {
      label: 'Distance (m)',
      value: fitnessData.distance.toFixed(2),
      icon: 'map-marker-distance',
      description: 'Distance covered today',
    },
    {
      label: 'Active Calories',
      value: fitnessData.calories.active.toFixed(2),
      icon: 'fire',
      description: 'Active calories burned today',
    },
    {
      label: 'Breakfast',
      value: fitnessData.calories.total.toFixed(2),
      icon: 'fire',
      description: (
        <View>
          <Text style={styles.boxLabel}>Cheesy Omelette with Broccoli</Text>
          <Text style={{ color: colors?.grey_600 }}>
            <Text
              style={{
                color: colors?.secondary_curry,
                marginHorizontal: 10,
                letterSpacing: 5,
              }}
            >
              ●{' '}
            </Text>{' '}
            {/* {userInfo ? (
    <>
      <Text>Welcome, {userInfo.data.user.name}</Text>
      <Text>Email: {userInfo.data.user.email}</Text>
      {userInfo.data.user.photo && (
        <Image source={{ uri: userInfo.data.user.photo }} style={styles.userImage} />
      )}
      <Button title="Sign Out" onPress={signOut} />
    </>
  ) : ( */}
            p 12
            <Text
              style={{
                color: colors?.secondary_morning_sea,
                marginHorizontal: 10,
                letterSpacing: 5,
              }}
            >
              {' '}
              ●{' '}
            </Text>
            p 12
            <Text
              style={{
                color: colors?.secondary_curry,
                marginHorizontal: 10,
                letterSpacing: 5,
              }}
            >
              ●{' '}
            </Text>
            p 12
          </Text>
        </View>
      ),
    },
    {
      label: 'Total Calories',
      value: fitnessData.calories.total.toFixed(2),
      icon: 'fire',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Heart Rates',
      value: fitnessData.heartRate.length,
      icon: 'heart-pulse',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Sleep Sessions',
      value: sleepToDisplay,
      icon: 'sleep',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Activities',
      value: fitnessData.activities.length,
      icon: 'run',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Weight Records',
      value: weightToDisplay,
      icon: 'scale-bathroom',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Blood Pressure',
      value: fitnessData.bloodPressure.length,
      icon: 'heart',
      description: 'Breakfast calories burned today',
    },
    {
      label: 'Blood Glucose',
      value: fitnessData.bloodGlucose.length,
      icon: 'water-percent',
      description: 'Breakfast calories burned today',
    },
  ];
  return (
    <View
      style={[styles.container, { backgroundColor: colors?.primary_cream }]}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingVertical: 16,
          paddingHorizontal: 20,
          borderBottomWidth: 0.7,
          borderBottomColor: colors?.grey_100,
        }}
      >
        <SimpleLineIcons
          name="refresh"
          onPress={async () => {
            setIsSyncing(true);
            setTimeout(() => {
              setIsSyncing(false);
            }, 3000);
            await stepSync();
            // await weightSync();
            // await sleepSync();
          }}
          size={20}
          color={route.params.theme?.colors?.grey_700}
        />
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <LeftArrow />
          <Text
            style={[
              fontStyles.Maison_600_18PX_21_6LH,
              { paddingHorizontal: 40 },
            ]}
          >
            Today
          </Text>
          <LeftArrow style={{ transform: [{ rotate: '180deg' }] }} />
        </View>
        <SimpleLineIcons
          name="settings"
          size={20}
          color={route.params.theme?.colors?.grey_700}
        />
      </View>
      <ScrollView contentContainerStyle={{ padding: 20, paddingTop: 0 }}>
        {!(isSyncing === null) && (
          <SyncCard
            colors={colors}
            lastSyncedAt="1:19 pm"
            isSyncing={isSyncing}
            onClose={() => {
              setIsSyncing(null);
              setWarningSync(true);
            }}
          />
        )}
        {loading ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loaderText}>Loading health data...</Text>
          </View>
        ) : (
          <View>
            <Text
              style={[fontStyles.Maison_600_20PX_28LH, { marginVertical: 10 }]}
            >
              Good Afternoon,user!
            </Text>
            {!(warningSync === null) && (
              <MotivationCard
                title="You're off to a slow start today"
                message="You’re at just 12% of your step goal. Want to plan a walk?"
                onClose={() => setWarningSync(null)}
              />
            )}

            <EnergyMeter
              target={-50}
              current={140}
              min={-500}
              max={500}
              targetLabel="🎯 Target: 250 kcal deficit"
            />
            <CommonCard style={{ marginVertical: 10 }}>
              <Text style={fontStyles.Maison_600_18PX_24LH}>Summary</Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 5,
                }}
              >
                {[
                  {
                    title: 'Kcal consumed',
                    progress: 90,
                    color: '#FF3F1F',
                    left: 123,
                    totalTask: 230,
                    icon: KcaConsumedIcon3,
                    completed: 12,
                  },
                  {
                    title: 'Kcal consumed',
                    progress: 20,
                    color: '#AC7950',
                    left: 123,
                    icon: KcaConsumedIcon2,
                    totalTask: 230,
                    completed: 12,
                  },
                  {
                    title: 'Kcal consumed',
                    progress: 50,
                    color: '#62656A',
                    left: 123,
                    totalTask: 230,
                    completed: 12,
                    icon: KcaConsumedIcon1,
                  },
                ].map((item, index) => (
                  <View
                    key={index}
                    style={{
                      flex: 1,
                      alignItems: 'center',
                      alignContent: 'center',
                    }}
                  >
                    <CircularProgress
                      percentage={item?.progress}
                      progressSize={60}
                      label="Kcal consumed"
                      left="458"
                      total="1200 kcal"
                      consume="800"
                      strokeColor={item?.color}
                    >
                      <item.icon height={25} width={25} />
                    </CircularProgress>
                  </View>
                ))}
              </View>
            </CommonCard>
            <CommonCard style={{ marginVertical: 10, marginBottom: 0 }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  // alignItems: 'center',
                  // marginBottom: 30,
                  // paddingTop: 0,
                }}
              >
                <Text style={fontStyles.Maison_600_18PX_24LH}>
                  Food & Activities
                </Text>
                <InfoIcon />
              </View>
              <View style={{ padding: 10, paddingBottom: 0 }}>
                {metrics.map((item, index) => (
                  <View
                    key={index + 'matrics'}
                    style={{
                      flexDirection: 'row',
                      borderBottomWidth: index === metrics.length - 1 ? 0 : 0.5,
                      alignItems: 'center',
                      justifyContent: 'space-evenly',
                      paddingVertical: 12,
                      borderColor: '#DCD5D7',
                    }}
                  >
                    <MaterialCommunityIcons
                      name={item.icon}
                      size={28}
                      style={{ flex: 1 }}
                      color="#FF3F1F"
                    />
                    <View style={{ flex: 4 }}>
                      <Text style={fontStyles.Maison_600_16PX_22LH}>
                        {item.label}
                      </Text>
                      {typeof item.description === 'string' ? (
                        <Text style={styles.boxLabel}>{item.description}</Text>
                      ) : (
                        item.description
                      )}
                    </View>
                    <View style={{ flex: 1.5 }}>
                      <Text
                        style={[
                          styles.boxLabel,
                          fontStyles.Maison_600_14PX_18LH,
                          {
                            textAlign: 'right',
                            color: [2, 4, 5].includes(index)
                              ? colors?.red_600
                              : colors?.secondary_avocado,
                          },
                        ]}
                      >
                        {[2, 4, 5].includes(index) ? '-' : '+'}
                        {index * 20} kcal
                        {/* {item.value} kcal */}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            </CommonCard>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginVertical: 10,
    padding: 16,
  },
  boxLabel: { color: '#555', fontSize: 14, marginTop: 8 },
  boxValue: { color: '#222', fontSize: 20, fontWeight: 'bold', marginTop: 4 },
  container: { flex: 1 },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  header: { color: '#fff', fontSize: 24, marginBottom: 5, textAlign: 'center' },
  loaderContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginTop: 100,
  },

  loaderText: { color: '#fff', marginTop: 10 },
});

export default MainScreen;
