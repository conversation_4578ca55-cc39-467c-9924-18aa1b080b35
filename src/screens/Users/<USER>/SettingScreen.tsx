import { View, ScrollView, Text } from 'react-native';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderWidthStyles,
  globalStyles,
} from '../../../theme/styles/globalStyles';
import { useTheme } from '../../../theme';
import {
  GreetingCard,
  CardBorder,
  CardTitle,
  CardContent,
} from '../../../components/Settings/Card';
import { fontStyles } from '../../../theme/fonts';
import {
  Wallet,
  Bell,
  MealBowlIcon,
  Chat,
  Headphone,
  InfoIcon,
  Location,
  Banknote,
  Refer,
} from '../../../assets/images';
import { PrimaryBtn } from '../../../components/Buttons/Btns';

export default function SettingScreen() {
  const color = useTheme();
  return (
    <ScrollView removeClippedSubviews={true}>
      <View
        style={[
          paddingStyles.p20,
          globalStyles.flex1,
          {
            backgroundColor: color.primary_cream,
          },
        ]}
      >
        <View style={gapStyles.gap_16}>
          <GreetingCard />

          <CardBorder style={globalStyles.flexDirectionRow}>
            <View style={gapStyles.gap_16}>
              <Text style={fontStyles.Maison_600_18PX_22LH}>
                Jonathan Green
              </Text>
              <Text
                style={[
                  fontStyles.Maison_400_14PX_16LH,
                  {
                    color: color.grey_7A7A7A,
                  },
                ]}
              >
                <EMAIL>
              </Text>
              <Text
                style={[
                  fontStyles.Maison_400_14PX_16LH,
                  {
                    color: color.grey_7A7A7A,
                  },
                ]}
              >
                +971549424373
              </Text>
            </View>
            <Text
              style={[
                {
                  color: color.primary_grenade,
                },
                fontStyles.Maison_600_16PX_20LH,
              ]}
            >
              Edit
            </Text>
          </CardBorder>

          <CardBorder>
            <CardTitle title="Meal plan & addresses" />
            <CardContent
              Icon={
                <MealBowlIcon
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'My Subscription'}
            />
            <CardContent
              Icon={
                <MealBowlIcon
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'Ingredient Dislike'}
            />
            <CardContent
              Icon={
                <Location
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'Manage Address'}
              showBorder={false}
            />
          </CardBorder>

          <CardBorder>
            <CardTitle title="My Wallet" />
            <CardContent
              Icon={
                <Wallet height={20} width={20} color={color.primary_grenade} />
              }
              text={'Payment History'}
            />
            <CardContent
              Icon={
                <Refer height={20} width={20} color={color.primary_grenade} />
              }
              text={'Refer & Earn'}
            />
            <CardContent
              Icon={
                <Banknote
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'Payment methods'}
              showBorder={false}
            />
          </CardBorder>

          <CardBorder>
            <CardTitle title="App Settings" />
            <CardContent
              Icon={
                <Bell height={20} width={20} color={color.primary_grenade} />
              }
              text={'Notifications'}
            />
            <CardContent
              Icon={
                <Chat height={20} width={20} color={color.primary_grenade} />
              }
              text={'Communication preferences'}
              showBorder={false}
            />
          </CardBorder>

          <CardBorder>
            <CardContent
              Icon={
                <Headphone
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'Contact us'}
            />
            <CardContent
              Icon={
                <InfoIcon
                  height={20}
                  width={20}
                  color={color.primary_grenade}
                />
              }
              text={'About'}
              showBorder={false}
            />
          </CardBorder>

          <PrimaryBtn
            style={[
              {
                backgroundColor: color.transparent,
                borderColor: color.primary_grenade,
              },
              borderWidthStyles.bw2,
            ]}
            text={'Logout'}
            textStyle={[
              {
                color: color.primary_grenade,
              },
              fontStyles.Maison_600_18PX_22LH,
            ]}
          />
        </View>
      </View>
    </ScrollView>
  );
}
