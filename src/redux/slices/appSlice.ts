import { createSlice } from '@reduxjs/toolkit';
import { AppSliceProps } from 'app-actions';
import {
  analyzeFood,
  getDeliveryArea,
  getDeliverySlot,
  getMealPreferences,
} from '../action/appActions';

const initialState: AppSliceProps = {
  loader: false,
  masterRmsData: [],
  deliveryArea: [],
  deliverySlots: [],
  masterData: undefined,
  cityList: [],
  isKeybordshow: false,
  lanKey: 'en',
  isRTL: false,
  masterIng: undefined,
  getMealPreferencesData: [],
  analyzeFoodResult: null,
};
const appSlices = createSlice({
  name: 'app',
  initialState,
  reducers: {
    getMasterData: (state) => {
      state.loader = true;
    },
    getMasterDataSucess: (state, { payload }) => {
      state.loader = false;
      state.masterData = payload;
    },
    analyzeFoodSuccess: (state, { payload }) => {
      state.loader = false;
      state.analyzeFoodResult = payload;
    },
    getIngredientData: (state) => {
      state.loader = true;
    },
    getIngredientDataSucess: (state, { payload }) => {
      state.loader = false;
      state.masterIng = payload;
    },
    getMasterRmsDataSucess: (state, { payload }) => {
      state.loader = false;
      state.masterRmsData = payload;
    },
    logout: (state) => {
      state.deliveryArea = [];
      state.deliverySlots = [];
      state.cityList = [];
    },
    getDeliveryAreaSuccess: (state, { payload }) => {
      state.loader = false;
      state.deliveryArea = payload;
    },
    getDeliverySlotSuccess: (state, { payload }) => {
      state.loader = false;
      state.deliverySlots = payload;
    },
    clearDeliverySlotSuccess: (state) => {
      state.deliverySlots = [];
    },
    getAllCity: (state) => {
      state.loader = true;
      state.cityList = [];
    },
    getAllCitySuccess: (state, { payload }) => {
      state.loader = false;
      state.cityList = payload;
    },
    setKeyboardState: (state, { payload }) => {
      state.isKeybordshow = payload;
    },
    setLanguage: (state, { payload }) => {
      const lang = payload;
      state.lanKey = lang;
      state.isRTL = lang === 'ar';
    },
    getMealPreferencesSuccess: (state, { payload }) => {
      state.loader = false;
      state.getMealPreferencesData = payload;
    },
  },
  extraReducers(builder) {
    builder.addCase(getMealPreferences, (state) => {
      return {
        ...state,
        loader: true,
        getMealPreferencesData: [],
      };
    });
    builder.addCase(getDeliveryArea, (state) => {
      return {
        ...state,
        loader: true,
        deliveryArea: [],
      };
    });
    builder.addCase(analyzeFood, (state) => {
      state.loader = true;
      state.analyzeFoodResult = null;
    });

    builder.addCase(getDeliverySlot, (state) => {
      return {
        ...state,
        loader: true,
        deliverySlots: [],
      };
    });
  },
});

export const {
  setLanguage,
  getMasterData,
  getMasterDataSucess,
  getMasterRmsDataSucess,
  logout,
  getDeliveryAreaSuccess,
  getDeliverySlotSuccess,
  getAllCity,
  getAllCitySuccess,
  clearDeliverySlotSuccess,
  setKeyboardState,
  getIngredientData,
  getIngredientDataSucess,
  getMealPreferencesSuccess,
  analyzeFoodSuccess,
} = appSlices.actions;
export default appSlices.reducer;
