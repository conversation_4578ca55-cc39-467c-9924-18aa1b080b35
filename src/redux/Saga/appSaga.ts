import { AxiosError, AxiosResponse } from 'axios';
import { call, put, takeLatest } from 'redux-saga/effects';
import {
  analyzeFoodApi,
  getAllCityApi,
  getAppMasterDataApi,
  getAppMasterRmsDataApi,
  getDeliveryAreaApi,
  getDeliverySlotApi,
  getIngredientDataApi,
  getMealPreferencesApi,
} from '../../utils/api';
import {
  analyzeFood,
  getDeliveryArea,
  getDeliverySlot,
  getMealPreferences,
} from '../action/appActions';
import {
  analyzeFoodSuccess,
  getAllCity,
  getAllCitySuccess,
  getDeliveryAreaSuccess,
  getDeliverySlotSuccess,
  getIngredientData,
  getIngredientDataSucess,
  getMasterData,
  getMasterDataSucess,
  getMasterRmsDataSucess,
} from '../slices/appSlice';

function* analyzeFoodSaga(action: ReturnType<typeof analyzeFood>) {
  console.log('analyzeFoodSaga triggered', action);

  try {
    console.log(' Calling analyzeFoodApi...');
    const response: AxiosResponse = yield call(analyzeFoodApi, action.payload);

    console.log(' API response:', response?.data);

    yield put(analyzeFoodSuccess(response.data));
  } catch (error) {
    if (error instanceof AxiosError) {
      console.error(' AxiosError:', {
        message: error.message,
        code: error.code,
        config: error.config,
        response: error.response,
      });
    } else {
      console.error(' General error:', error);
    }
  }
}

function* getMasterDataSaga() {
  try {
    const response: AxiosResponse = yield call(getAppMasterDataApi);
    const response3: AxiosResponse = yield call(getAppMasterRmsDataApi);
    if (response?.data?.data) {
      yield put(getMasterDataSucess(response?.data?.data));
    }
    if (response3?.data?.data) {
      yield put(getMasterRmsDataSucess(response3?.data?.data));
    }
  } catch (error) {
    console.error('Error in get Master Data', error);
  }
}
function* getAllCitySaga() {
  try {
    const response: AxiosResponse = yield call(getAllCityApi);
    if (response?.data?.data) {
      yield put(getAllCitySuccess(response?.data?.data));
    }
  } catch (error) {
    console.error('Error in get All City', error);
    yield put(getAllCitySuccess([]));
  }
}

function* getDeliveryAreaSaga(action: ReturnType<typeof getDeliveryArea>) {
  try {
    const response: AxiosResponse = yield call(
      getDeliveryAreaApi,
      action?.payload,
    );
    if (response?.data?.data) {
      yield put(getDeliveryAreaSuccess(response?.data?.data?.area_list));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getDeliveryAreaSuccess([]));
      console.error('Error in Delivery Area Saga', error?.response?.data);
    }
  }
}
function* getDeliverySlotSaga(action: ReturnType<typeof getDeliverySlot>) {
  try {
    const response: AxiosResponse = yield call(
      getDeliverySlotApi,
      action?.payload.city,
      action?.payload.area,
    );
    if (response?.data?.data) {
      yield put(getDeliverySlotSuccess(response?.data?.data?.slot_list));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getDeliverySlotSuccess([]));
    }
    console.error('Error in Delivery Slot', error);
  }
}
function* getIngredientDataSaga() {
  try {
    const response: AxiosResponse = yield call(getIngredientDataApi);
    if (response?.data?.data) {
      yield put(getIngredientDataSucess(response?.data?.data));
    }
  } catch (error) {
    console.error('getIngredientDataSaga API Error:- ', error);
  }
}

function* getMealPreferencesSaga(
  action: ReturnType<typeof getMealPreferences>,
) {
  try {
    const response: AxiosResponse = yield call(
      getMealPreferencesApi,
      action?.payload,
    );
    if (response.data.status) {
      yield put(getMealPreferences(response?.data?.data));
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      yield put(getMealPreferences(error?.response?.data));
      console.error('Error in getMealPreferencesSaga', error?.response?.data);
    }
  }
}
export function* watchAppSagas(): Generator {
  yield takeLatest(getMasterData, getMasterDataSaga);
  yield takeLatest(getAllCity, getAllCitySaga);
  yield takeLatest(getDeliveryArea, getDeliveryAreaSaga);
  yield takeLatest(getDeliverySlot, getDeliverySlotSaga);
  yield takeLatest(getIngredientData, getIngredientDataSaga);
  yield takeLatest(getMealPreferences, getMealPreferencesSaga);
  yield takeLatest(analyzeFood, analyzeFoodSaga);
}
