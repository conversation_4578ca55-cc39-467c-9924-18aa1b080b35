import { createAction } from '@reduxjs/toolkit';
import { GetDeliverySlotProps, GetMealPreferencesPayload } from 'actions';
import { EventDataProps } from 'app-actions';

export const analyzeFood = createAction(
  'app/analyzeFood',
  (payload: {
    file: {
      uri: string;
      name: string;
      type: string;
    };
    user_id: string;
    analysis_mode: string;
  }) => ({
    payload,
  }),
);

export const trackEvent = createAction(
  'app/trackEvent',
  (eventName: string, eventData: EventDataProps) => {
    return {
      payload: {
        eventName: eventName,
        eventData: eventData,
      },
    };
  },
);
export const getDeliveryArea = createAction(
  'app/getDeliveryArea',
  (cityname: string) => {
    return {
      payload: cityname,
    };
  },
);
export const getDeliverySlot = createAction(
  'app/getDeliverySlot',
  (payload: GetDeliverySlotProps) => {
    return {
      payload: payload,
    };
  },
);

export const getMealPreferences = createAction(
  'app/getMealPreferences',
  (payload: GetMealPreferencesPayload) => {
    return {
      payload: payload,
    };
  },
);
