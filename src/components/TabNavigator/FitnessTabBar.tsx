import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import React, { FC } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { HomeIcon, MessageSVG, PlusOrangeIcon } from '../../assets/images';
import { getCurrentRouteName } from '../../navigation/AppNavigator';
import { RootState } from '../../redux/store';
import LogOptionsSheet from '../../screens/fitness/logoptions';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { globalStyles } from '../../theme/styles/globalStyles';
const FitnessTabBar: FC<BottomTabBarProps> = () => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const colors = useTheme();
  const currentPage = getCurrentRouteName();
  const navigation = useNavigation();
  const [isSheetOpen, setIsSheetOpen] = React.useState(false);

  return (
    <View
      style={[
        isRTL ? globalStyles.rowReverse : globalStyles.row,
        {
          backgroundColor: colors?.neutral_white,
        },
        tabsStyles.bottomtabWrapper,
      ]}
    >
      <Pressable
        onPress={() => {
          navigation.navigate('Home');
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              backgroundColor:
                currentPage === 'Home' ? colors?.FFDDD199 : undefined,
            },
          ]}
        >
          <HomeIcon
            width={RFont(27)}
            height={RFont(27)}
            color={
              currentPage === 'Account'
                ? colors?.primary_grenade
                : colors?.primary_spinach
            }
          />
        </View>
        <Text style={[fontStyles.Maison_600_12PX_18LH]}>Home</Text>
      </Pressable>
      <Pressable
        onPress={() => {
          setIsSheetOpen(true);
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              borderWidth: 3,
              borderRadius: RFont(500),
              borderColor: colors?.primary_grenade,
              padding: RFont(1),
              marginBottom: RFont(5),
            },
          ]}
        >
          <PlusOrangeIcon width={RFont(25)} height={RFont(25)} />
        </View>
      </Pressable>
      <Pressable
        onPress={() => {
          navigation.navigate('Coach');
        }}
        style={tabsStyles.iconWrapper}
      >
        <View
          style={[
            tabsStyles.iconstyle,
            {
              backgroundColor:
                currentPage === 'Coach' ? colors?.FFDDD199 : undefined,
            },
          ]}
        >
          <MessageSVG
            width={RFont(27)}
            height={RFont(27)}
            color={
              currentPage === 'Coach'
                ? colors?.primary_grenade
                : colors?.primary_spinach
            }
          />
        </View>
        <Text style={[fontStyles.Maison_600_12PX_18LH]}>Coach</Text>
      </Pressable>
      <LogOptionsSheet
        open={isSheetOpen}
        onClose={() => setIsSheetOpen(false)}
      />
    </View>
  );
};
const tabsStyles = StyleSheet.create({
  bottomtabWrapper: {
    borderTopLeftRadius: RFont(20),
    borderTopRightRadius: RFont(20),
    elevation: RFont(4),
    paddingHorizontal: RFont(16),
    paddingVertical: RFont(12),
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: RFont(4) },
    shadowOpacity: 0.05,
    shadowRadius: RFont(4),
  },
  iconWrapper: { alignItems: 'center', alignSelf: 'center', flex: 1 },
  iconstyle: {
    borderRadius: RFont(12),
    overflow: 'hidden',
    padding: RFont(8),
  },
});
export default FitnessTabBar;
