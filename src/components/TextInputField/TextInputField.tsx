import { FormInputProps } from 'componentsProps';
import React, { useState } from 'react';
import { Text, TextInput, View } from 'react-native';
import { useSelector } from 'react-redux';
import { SearchIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles, widthStyles } from '../../theme/styles/globalStyles';
import { formInputStyles } from '../../theme/styles/inputsStyles';

export const TextInputField: React.FC<FormInputProps> = ({
  error,
  wrapperStyle,
  inputStyles,
  placeholder = '',
  value = undefined,
  onChangeText = undefined,
  leftIcon,
  header,
  keyboardType = 'ascii-capable',
  disabled,
  autoFocus,
  headerStyle,
  containerStyle,
  numberOfLines = 0,
  touched,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isFocused, setIsFocused] = useState(false);
  const colors = useTheme();
  return (
    <View style={[gapStyles?.gap_8, containerStyle]}>
      {header && (
        <Text
          style={[
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_18LH,
            { color: colors?.neutral_black },
            headerStyle,
          ]}
        >
          {header}
        </Text>
      )}
      <View
        style={[
          formInputStyles.input,
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          {
            backgroundColor: colors?.neutral_white,
            borderColor: error
              ? colors?.red_500
              : isFocused
                ? colors?.grey_900
                : colors?.grey_100,
          },
          wrapperStyle,
        ]}
      >
        {leftIcon}
        <TextInput
          style={[
            isRTL
              ? globalStyles.writingDirectionRight
              : globalStyles.writingDirectionLeft,
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            widthStyles.w100,
            fontStyles.Maison_500_16PX_18LH,
            {
              color: disabled ? colors?.grey_600 : colors?.black_900,
            },
            inputStyles,
          ]}
          placeholder={placeholder}
          keyboardType={keyboardType}
          placeholderTextColor={colors?.grey_600}
          onChangeText={onChangeText}
          value={value}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          autoFocus={autoFocus}
          multiline={numberOfLines > 0}
          numberOfLines={numberOfLines}
        />
      </View>
      {error && touched && (
        <Text
          style={[fontStyles.Maison_500_14PX_18LH, { color: colors?.red_500 }]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export const SearchBar: React.FC<FormInputProps> = ({
  error,
  wrapperStyle,
  inputStyles,
  placeholder = '',
  value = undefined,
  onChangeText = undefined,
  keyboardType = 'ascii-capable',
  disabled,
  autoFocus,
  containerStyle,
  numberOfLines = 0,
  touched,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [isFocused, setIsFocused] = useState(false);
  const colors = useTheme();
  return (
    <View style={containerStyle}>
      <View
        style={[
          gapStyles.gap_12,
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          formInputStyles.input,
          {
            borderColor: error
              ? colors?.red_500
              : isFocused
                ? colors?.grey_900
                : colors?.grey_100,
          },
          paddingStyles.py8,
          wrapperStyle,
        ]}
      >
        <SearchIcon />
        <TextInput
          style={[
            isRTL
              ? globalStyles.writingDirectionRight
              : globalStyles.writingDirectionLeft,
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            fontStyles.Maison_500_16PX_18LH,
            {
              color: disabled ? colors?.grey_600 : colors?.grey_700,
              backgroundColor: colors?.neutral_white,
            },
            inputStyles,
          ]}
          placeholder={placeholder}
          keyboardType={keyboardType}
          placeholderTextColor={colors?.grey_600}
          onChangeText={onChangeText}
          value={value}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          autoFocus={autoFocus}
          multiline={numberOfLines > 0}
          numberOfLines={numberOfLines}
        />
      </View>
      {error && touched && (
        <Text
          style={[
            fontStyles.Maison_500_14PX_18LH,
            marginStyles.mt_8,
            { color: colors?.red_500 },
          ]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};
