import messaging from '@react-native-firebase/messaging';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import { Image, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { GoogleLogo } from '../../../assets/images';
import { RootState } from '../../../redux/store';
import {
  ensureUserModuleSyncEntries,
  supabase,
} from '../../../screens/fitness/supabaseClient';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../../theme/styles/globalStyles';
import { PrimaryBtn } from '../../Buttons/Btns';

export const WelcomeCard = () =>
  // navigation: NavigationProp<RootGuestStackParamList>,
  {
    const { translation } = useSelector((state: RootState) => state.auth);
    const { isRTL } = useSelector((state: RootState) => state.app);
    const colors = useTheme();
    const navigation = useNavigation();
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const signIn = async () => {
      try {
        setLoading(true);
        setError(null);

        await GoogleSignin.hasPlayServices();
        const usrInfo = await GoogleSignin.signIn();
        const idToken = usrInfo?.data?.idToken ?? '';
        const fcmToken = await messaging().getToken();
        console.log('FCM Token:', fcmToken);

        // Authenticate with Supabase
        const { error: authError } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: idToken,
        });

        if (authError) {
          throw new Error(`Supabase Auth Error: ${authError.message}`);
        }

        // Get authenticated user
        const {
          data: { user },
          error: userFetchError,
        } = await supabase.auth.getUser();

        if (!user || userFetchError) {
          throw new Error('Failed to get authenticated user');
        }

        // Check for existing user
        const { data: existingUser, error: existingUserError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (existingUserError && existingUserError.code !== 'PGRST116') {
          throw new Error(
            `Error checking existing user: ${existingUserError.message}`,
          );
        }

        if (!existingUser) {
          const { error: insertError } = await supabase.from('users').insert({
            id: user.id,
            email: user.email,
            full_name:
              user?.user_metadata?.full_name || usrInfo?.data?.user?.name,
            created_at: new Date().toISOString(),
            fcm_token: fcmToken,
          });

          if (insertError) {
            throw new Error(`Error inserting new user: ${insertError.message}`);
          }

          console.log('✅ New user profile created');
        } else {
          // Update FCM token if user exists
          const { error: updateError } = await supabase
            .from('users')
            .update({ fcm_token: fcmToken })
            .eq('id', user.id);

          if (updateError) {
            console.warn(
              `⚠️ Failed to update FCM token: ${updateError.message}`,
            );
          } else {
            console.log('✅ FCM token updated');
          }
        }

        // Insert default fitness profile if not exists
        const { data: existingUserFitnessData } = await supabase
          .from('user_fitness_profiles')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (!existingUserFitnessData) {
          const defaultFitnessData = {
            daily_goals_calories_burned: 500,
            daily_goals_steps: 7000,
            height: 178,
            gender: 'Male',
            date_of_birth: null,
            created_at: new Date().toISOString(),
            occupation: 'Software Engineer / Tech Entrepreneur',
            fitness_level: 'Beginner',
            experience_years: 0,
            past_experience: 'Mostly sedentary with occasional walks',
            limitations: 'Mild lower back pain',
            goals_set: 'Yes',
            primary_goal: 'Fat loss',
            secondary_goals: 'Improve flexibility, posture, and sleep quality',
            preferred_exercises:
              'Walking, light mobility drills, bodyweight exercises, swimming',
            availability: '2–3 days/week, mornings preferred',
            duration: '45–60 minutes',
            pain_points: 'Consistency and diet adherence due to work schedule',
            activity_level: 'Slightly Active',
          };

          const { error: fitnessInsertError } = await supabase
            .from('user_fitness_profiles')
            .insert({ user_id: user.id, ...defaultFitnessData });

          if (fitnessInsertError) {
            throw new Error(
              `Error inserting fitness profile: ${fitnessInsertError.message}`,
            );
          }

          // Persona generation request
          await fetch('https://ai-coach.delicut.click/persona-generation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: user.id,
              user_data: { ...defaultFitnessData },
            }),
          });

          console.log('✅ Default fitness profile and persona created');
        }

        // Ensure user_module_sync entry
        const { data: existingSync } = await supabase
          .from('user_module_sync')
          .select('id')
          .eq('user_id', user.id)
          .eq('module_name', 'steps')
          .single();

        if (!existingSync) {
          const { error: insertSyncError } = await supabase
            .from('user_module_sync')
            .insert({
              user_id: user.id,
              module_name: 'steps',
              last_synced_at: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });

          if (insertSyncError) {
            throw new Error(
              `Error inserting user_module_sync: ${insertSyncError.message}`,
            );
          }
        }

        await ensureUserModuleSyncEntries(user.id);
        console.log('✅ All module sync entries ensured');

        // Navigate to main tab
        navigation.navigate('Tabs', { tokenRefresher: 'tokenRefresh' });
      } catch (err: any) {
        console.error('❌ Sign-in failed:', err);

        // Handle known Google errors
        switch (err.code) {
          case statusCodes.SIGN_IN_CANCELLED:
            console.log('User cancelled the login flow');
            break;
          case statusCodes.IN_PROGRESS:
            console.log('Sign-in already in progress');
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            console.log(
              'Google Play Services Error',
              'Please update or enable Google Play Services to continue.',
            );
            break;
          default:
            console.log(
              'Sign In Error',
              err.message || 'An unexpected error occurred.',
            );
        }

        setError(err);
      } finally {
        setLoading(false);
      }
    };

    return (
      <View
        style={[
          globalStyles.flex1,
          globalStyles.justifyContentEnd,
          paddingStyles.py12,
          paddingStyles.px16,
        ]}
      >
        <Text
          style={[
            fontStyles.Maison_600_48PX_53LH,
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            { color: colors?.neutral_white },
          ]}
        >
          {translation.YOUR_WELLNESS_JOURNEY_STARTS_HERE}
        </Text>
        <PrimaryBtn
          text={translation.LOGIN_WITH_GOOGLE}
          onPress={signIn}
          isLoading={loading}
          leftIcon={
            <Image
              source={GoogleLogo}
              height={10}
              width={10}
              style={{ width: 20, height: 20 }}
            />
          }
          style={[marginStyles.mt_32, marginStyles.mb_24]}
          textStyle={[
            { color: colors?.black },
            fontStyles.Maison_600_16PX_19_2LH,
          ]}
          // focusColor={colors?.primary_grenade}
          backgroundColor={colors?.neutral_white}
        />
      </View>
    );
  };
