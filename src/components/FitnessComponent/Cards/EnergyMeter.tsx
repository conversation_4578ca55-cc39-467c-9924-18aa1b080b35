import React, { memo } from 'react';
import {
  LayoutChangeEvent,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Flag, InfoIcon, Mark } from '../../../assets/images';
import { fontStyles } from '../../../theme/fonts';
import { CommonCard } from '../../Card/Card';

type EnergyMeterProps = {
  target: number;
  current: number;
  min?: number;
  max?: number;
  style?: ViewStyle;
  targetLabel?: string;
  gradientColors?: string[];
};

const EnergyMeter = ({
  target,
  current,
  min = -500,
  max = 500,
  style,
  targetLabel = '🎯 Target: 500 kcal deficit',
  gradientColors = ['#4CAF50', '#FFC107', '#FF5722'],
}: EnergyMeterProps) => {
  const progressBarWidth = useSharedValue(0);

  const onMeterLayout = (event: LayoutChangeEvent) => {
    progressBarWidth.value = withTiming(event.nativeEvent.layout.width, {
      duration: 500,
    });
  };

  const animatedTargetStyle = useAnimatedStyle(() => ({
    left: progressBarWidth.value * ((target - min) / (max - min)) - 6,
  }));

  const animatedCurrentStyle = useAnimatedStyle(() => ({
    left: progressBarWidth.value * ((current - min) / (max - min)) - 6,
  }));

  const isSurplus = current > 0;

  return (
    <CommonCard style={[{ marginVertical: 10 }, style]}>
      <View style={styles.header}>
        <Text style={fontStyles.Maison_600_18PX_24LH}>
          Daily calorie balance{' '}
          <Text style={[fontStyles.Maison_600_18PX_24LH, { color: 'gray' }]}>
            ●
          </Text>
        </Text>
        <InfoIcon />
      </View>

      <Text style={[fontStyles.Maison_400_12PX_16LH, { color: 'gray' }]}>
        {targetLabel}
      </Text>

      <View style={styles.meterContainer} onLayout={onMeterLayout}>
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientLine}
        />

        <Animated.View style={[styles.markerContainer, animatedTargetStyle]}>
          <Flag />
        </Animated.View>

        <Animated.View style={[styles.markerContainer, animatedCurrentStyle]}>
          <Text style={[fontStyles.Maison_600_32PX_40LH, { color: '#CC0000' }]}>
            {current > 0 ? `+${current}` : current}{' '}
            <Text
              style={[
                fontStyles.Maison_400_14PX_18LH,
                { lineHeight: 40, color: '#CC0000' },
              ]}
            >
              kcal
            </Text>
          </Text>
          <Mark />
        </Animated.View>
      </View>

      <View style={styles.legend}>
        <Text style={[fontStyles.Maison_500_12PX_16LH, { color: '#006600' }]}>
          Deficit
        </Text>
        <Text style={[fontStyles.Maison_500_12PX_16LH, { color: '#CC0000' }]}>
          Surplus
        </Text>
      </View>

      {isSurplus && (
        <View style={styles.warningBox}>
          <Text style={[fontStyles.Maison_400_12PX_14LH, styles.warningText]}>
            You’ve consumed 500 more calories than you’ve burned so far today. A
            short walk or lighter meal can help bring you back on track. 💪
          </Text>
        </View>
      )}
    </CommonCard>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 8,
  },
  meterContainer: {
    flex: 1,
    position: 'relative',
    height: 90,
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  gradientLine: {
    height: 10,
    borderRadius: 10,
  },
  markerContainer: {
    position: 'absolute',
    alignItems: 'center',
  },
  legend: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 1,
    marginTop: 5,
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: '#FFE9E5',
    marginTop: 16,
    padding: 10,
    borderRadius: 10,
  },
  warningIcon: {
    paddingHorizontal: 5,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: '#19191A',
  },
});

export default memo(EnergyMeter);
