import { useCallback } from 'react';
import { Platform } from 'react-native';
import BrokenHealthKit from 'react-native-health'; // iOS
import { readRecords } from 'react-native-health-connect'; // Android only
import { syncIosStepsToSupabase, syncStepsToSupabase, updateLastSyncedAt } from '../../../screens/fitness/supabaseClient';
import { fetchNotifications, getStepsDataFromDb } from '../../../utils/fitnessFunctions';



const NativeModules = require("react-native").NativeModules;
const AppleHealthKit = NativeModules.AppleHealthKit as typeof BrokenHealthKit;
export function useStepSync(
  setStepsFromDb: (steps: number) => void,
  userId: string | null,
) {
  return useCallback(async () => {
    if (!userId) {
      console.warn('[useStepSync] No userId provided');
      return;
    }

    const start = new Date();
    start.setHours(0, 0, 0, 0);

    const now = new Date();
    now.setHours(23, 59, 59, 999);

    try {
      if (Platform.OS === 'android') {
        const stepsRes = await readRecords('Steps', {
          timeRangeFilter: {
            operator: 'between',
            startTime: start.toISOString(),
            endTime: now.toISOString(),
          },
        });

        if (stepsRes.records?.length > 0) {
          await syncStepsToSupabase(
            stepsRes.records,
            userId,
            start.toISOString(),
            now.toISOString(),
          );
          await updateLastSyncedAt(userId, 'steps', now.toISOString());
        }
      }

      if (Platform.OS === 'ios') {
        const options = {
          startDate: start.toISOString(),
          endDate: now.toISOString(),
        };

        const stepSamples = await new Promise<AppleHealthKit.StatisticsResponse[]>((resolve, reject) => {
          AppleHealthKit.getDailyStepCountSamples(options, (err, results) => {
            if (err) {
              reject(err);
            } else {
              resolve(results);
            }
          });
        });

        console.log(JSON.stringify(stepSamples),'check61');

        if (stepSamples.length > 0) {
          await syncIosStepsToSupabase(
            [
              {
                count: stepSamples,
                startTime: start.toISOString(),
                endTime: now.toISOString(),
              },
            ],
            userId,
            start.toISOString(),
            now.toISOString(),
          );
          await updateLastSyncedAt(userId, 'steps', now.toISOString());
        }
      }

      const { data, error } = await getStepsDataFromDb(
        userId,
        start.toISOString(),
        now.toISOString(),
      );
      fetchNotifications(userId)
      setStepsFromDb(error ? 0 : (data?.[0]?.total_steps ?? 0));
    } catch (e) {
      console.error('[useStepSync] Error:', e);
    }
  }, [setStepsFromDb, userId]);
}
