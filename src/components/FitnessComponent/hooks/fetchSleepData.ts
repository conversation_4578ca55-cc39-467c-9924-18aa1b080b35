import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  getUserId,
  syncSleepSessionsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getSleepDataFromDb } from '../../../utils/fitnessFunctions';

export function useFetchSleepData(setFitnessData: any, setSleepFromDb: any) {
  return useCallback(
    async (sleepStartDate: Date | null, sleepEndDate: Date) => {
      if (
        !(sleepStartDate instanceof Date) ||
        !(sleepEndDate instanceof Date)
      ) {
        console.warn(
          '[useFetchSleepData] sleepStartDate or sleepEndDate is not valid Date:',
          sleepStartDate,
          sleepEndDate,
        );
        return;
      }
      if (sleepStartDate >= sleepEndDate) {
        console.warn(
          '[useFetchSleepData] sleepStartDate must be before sleepEndDate:',
          sleepStartDate,
          sleepEndDate,
        );
        return;
      }

      try {
        console.log(
          '[useFetchSleepData] Fetching sleep sessions from',
          sleepStartDate.toISOString(),
          'to',
          sleepEndDate.toISOString(),
        );

        const sleepRes = await readRecords('SleepSession', {
          timeRangeFilter: {
            operator: 'between',
            startTime: sleepStartDate.toISOString(),
            endTime: sleepEndDate.toISOString(),
          },
        });

        console.log(
          '[useFetchSleepData] Sleep sessions fetched:',
          sleepRes.records.length,
        );
        setFitnessData((fd: any) => ({ ...fd, sleep: sleepRes.records }));

        const userId = await getUserId();
        await syncSleepSessionsToSupabase(sleepRes.records, userId as string);

        await updateLastSyncedAt(
          userId as string,
          'sleep',
          sleepEndDate.toISOString(),
        );
        console.log(
          '[useFetchSleepData] Updated last synced time for sleep to:',
          sleepEndDate.toISOString(),
        );
        const { data, error } = await getSleepDataFromDb(userId as string);
        setSleepFromDb(error || !data?.length ? null : (data[0]?.minutes ?? 0));
      } catch (e) {
        console.error('[useFetchSleepData] Error fetching sleep data:', e);
      }
    },
    [setFitnessData, setSleepFromDb],
  );
}
