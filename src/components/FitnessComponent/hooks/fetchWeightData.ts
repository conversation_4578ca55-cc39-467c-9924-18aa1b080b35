import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  getUserId,
  syncWeightToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getWeightDataFromDb } from '../../../utils/fitnessFunctions';

export function useFetchWeightData(setFitnessData: any, setWeightFromDb: any) {
  return useCallback(
    async (weightStartDate: Date | null, weightEndDate: Date) => {
      if (
        !weightStartDate ||
        !weightEndDate ||
        weightStartDate >= weightEndDate
      ) {
        console.warn(
          '[useFetchWeightData] Invalid time range:',
          weightStartDate,
          weightEndDate,
        );
        return;
      }

      try {
        console.log(
          '[useFetchWeightData] Fetching weight from',
          weightStartDate.toISOString(),
          'to',
          weightEndDate.toISOString(),
        );

        const weightRes = await readRecords('Weight', {
          timeRangeFilter: {
            operator: 'between',
            startTime: weightStartDate.toISOString(),
            endTime: weightEndDate.toISOString(),
          },
        });

        console.log(
          '[useFetchWeightData] Weight records fetched:',
          weightRes?.records?.length,
        );
        setFitnessData((fd: any) => ({ ...fd, weight: weightRes.records }));

        const userId = await getUserId();
        await syncWeightToSupabase(weightRes.records, userId as string);

        await updateLastSyncedAt(
          userId as string,
          'weight',
          weightEndDate.toISOString(),
        );
        console.log(
          '[useFetchWeightData] Updated last synced time for weight to:',
          weightEndDate.toISOString(),
        );
        const { data, error } = await getWeightDataFromDb(userId as string);
        setWeightFromDb(
          error || !data?.length ? null : (data[0]?.weight_kg ?? 0),
        );
      } catch (e) {
        console.error('[useFetchWeightData] Error fetching weight data:', e);
      }
    },
    [setFitnessData, setWeightFromDb],
  );
}
