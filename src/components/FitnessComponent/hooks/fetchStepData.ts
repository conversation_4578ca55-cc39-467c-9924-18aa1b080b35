import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  getUserId,
  syncStepsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getStepsDataFromDb } from '../../../utils/fitnessFunctions';

export function useFetchStepData(setFitnessData: any, setStepsFromDb: any) {
  return useCallback(
    async (startDate: Date | null, endDate: Date) => {
      if (!startDate || !endDate || startDate >= endDate) {
        console.warn(
          '[useFetchStepData] Invalid time range:',
          startDate,
          endDate,
        );
        return;
      }
      try {
        console.log(
          '[useFetchStepData] Fetching steps from',
          startDate.toISOString(),
          'to',
          endDate.toISOString(),
        );

        const stepsRes = await readRecords('Steps', {
          timeRangeFilter: {
            operator: 'between',
            startTime: startDate.toISOString(),
            endTime: endDate.toISOString(),
          },
        });

        const steps = Array.isArray(stepsRes?.records)
          ? stepsRes.records.reduce((t, r) => t + (r.count ?? 0), 0)
          : 0;

        console.log('[useFetchStepData] Steps fetched:', steps);
        setFitnessData((fd: any) => ({ ...fd, steps }));

        const userId = await getUserId();
        console.log(
          '[useFetchStepData] Syncing steps to Supabase for user:',
          userId,
        );
        let start = new Date();
        start.setHours(0, 0, 0, 0);
        // const start = new Date(new Date(lastSyncedAt).getTime() - 60000);
        // const now = new Date();
        let now = new Date();
        now.setHours(23, 59, 59, 999);
        await syncStepsToSupabase(
          stepsRes.records,
          userId as string,
          start.toISOString(),
          now.toISOString(),
        );

        await updateLastSyncedAt(
          userId as string,
          'steps',
          endDate.toISOString(),
        );
        console.log(
          '[useFetchStepData] Updated last synced time to:',
          endDate.toISOString(),
        );

        const { data, error } = await getStepsDataFromDb(
          userId as string,
          start.toISOString(),
          now.toISOString(),
        );
        setStepsFromDb(error ? 0 : (data?.[0]?.total_steps ?? 0));
      } catch (e) {
        console.error('[useFetchStepData] Error fetching step data:', e);
      }
    },
    [setFitnessData, setStepsFromDb],
  );
}
