// useSleepSync.ts
import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  syncSleepSessionsToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getSleepDataFromDb } from '../../../utils/fitnessFunctions';
export function useSleepSync(
  setSleepFromDb: (sleep: number | null) => void,
  userId: string | null,
) {
  return useCallback(async () =>
    // lastSyncedAt: any
    {
      if (!userId) {
        console.warn('[useSleepSync] No userId provided');
        return;
      }
      try {
        let start = new Date();
        start.setHours(0, 0, 0, 0);
        // const now = new Date();
        let now = new Date();
        now.setHours(23, 59, 59, 999);

        const sleepRes = await readRecords('SleepSession', {
          timeRangeFilter: {
            operator: 'between',
            startTime: start.toISOString(),
            endTime: now.toISOString(),
          },
        });

        if (sleepRes.records?.length > 0) {
          await syncSleepSessionsToSupabase(sleepRes.records, userId);
          await updateLastSyncedAt(userId, 'sleep', now.toISOString());
        }

        const { data, error } = await getSleepDataFromDb(userId as string);

        setSleepFromDb(error || !data?.length ? null : (data[0]?.minutes ?? 0));
      } catch (e) {
        console.error('[useSleepSync] Error:', e);
      }
    }, [setSleepFromDb, userId]);
}
