// useWeightSync.ts
import { useCallback } from 'react';
import { readRecords } from 'react-native-health-connect';
import {
  syncWeightToSupabase,
  updateLastSyncedAt,
} from '../../../screens/fitness/supabaseClient';
import { getWeightDataFromDb } from '../../../utils/fitnessFunctions';

export function useWeightSync(
  setWeightFromDb: (weight: number | null) => void,
  userId: string | null,
) {
  return useCallback(async () =>
    // lastSyncedAt: any
    {
      if (!userId) {
        console.warn('[useWeightSync] No userId provided');
        return;
      }
      try {
        let start = new Date();
        start.setHours(0, 0, 0, 0);
        // const start = new Date(new Date(lastSyncedAt).getTime() - 60000);
        // const now = new Date()
        let now = new Date();
        now.setHours(23, 59, 59, 999);

        const weightRes = await readRecords('Weight', {
          timeRangeFilter: {
            operator: 'between',
            startTime: start.toISOString(),
            endTime: now.toISOString(),
          },
        });
        console.log(weightRes.records?.length, 'check weight records');

        if (weightRes.records?.length > 0) {
          await syncWeightToSupabase(weightRes.records, userId);
          await updateLastSyncedAt(userId, 'weight', now.toISOString());
        }

        const { data, error } = await getWeightDataFromDb(userId as string);
        setWeightFromDb(
          error || !data?.length ? null : (data[0]?.weight_kg ?? 0),
        );
      } catch (e) {
        console.error('[useWeightSync] Error:', e);
      }
    }, [setWeightFromDb, userId]);
}
