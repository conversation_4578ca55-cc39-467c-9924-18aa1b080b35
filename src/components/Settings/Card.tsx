import { Text, View } from 'react-native';
import {
  gapStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';

import {
  CardBorderProps,
  CardContentProps,
  CardTitleProps,
} from 'componentsProps';
import { useSelector } from 'react-redux';
import { ChevronRightIcon } from '../../assets/images';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  borderRadiusStyles,
  borderWidthStyles,
  globalStyles,
} from '../../theme/styles/globalStyles';

export const GreetingCard = () => {
  const colors = useTheme();
  const { translation, authUser } = useSelector(
    (state: RootState) => state?.auth,
  );
  const dt = new Date();
  const hour = dt.getHours();

  return (
    <View>
      <View style={[paddingStyles.py8, gapStyles.gap_4]}>
        <Text
          style={[
            fontStyles.Maison_500_16PX_18LH,
            {
              color: colors?.black_50_p,
            },
          ]}
        >
          {hour < 12
            ? translation.GOOD_MORNING
            : hour < 18
              ? translation.GOOD_AFTERNOON
              : translation.GOOD_EVENING}
        </Text>
        <Text style={fontStyles.Maison_600_32PX_40LH}>
          {authUser?.first_name || 'User'}
        </Text>
      </View>
    </View>
  );
};

export const CardBorder = ({ children, style }: CardBorderProps) => {
  const colors = useTheme();
  return (
    <View
      style={[
        {
          backgroundColor: colors?.neutral_white,
        },
        borderRadiusStyles.br16,
        globalStyles.justifyContentSpaceBetween,
        paddingStyles.p16,
        style,
      ]}
    >
      {children}
    </View>
  );
};

export const CardTitle = ({ title }: CardTitleProps) => {
  const colors = useTheme();
  return (
    <Text
      style={[
        fontStyles.Maison_600_16PX_20LH,
        paddingStyles.py8,
        {
          color: colors?.black_900,
        },
      ]}
    >
      {title}
    </Text>
  );
};

export const CardContent = ({
  Icon,
  text,
  showBorder = true,
}: CardContentProps) => {
  const colors = useTheme();
  return (
    <View
      style={[
        paddingStyles.py16,
        {
          backgroundColor: colors?.neutral_white,
        },
        globalStyles.justifyContentSpaceBetween,
        globalStyles.flexDirectionRow,
        globalStyles.alignItemsCenter,
        showBorder
          ? [
              borderWidthStyles.bbw1,
              {
                borderBottomColor: colors?.grey_0000000F,
              },
            ]
          : '',
      ]}
    >
      <View
        style={[
          gapStyles.gap_12,
          globalStyles.flexDirectionRow,
          globalStyles.alignItemsCenter,
        ]}
      >
        {Icon}
        <Text
          style={[
            fontStyles.Maison_500_16PX_20,
            {
              color: colors?.black_900,
            },
          ]}
        >
          {text}
        </Text>
      </View>
      <ChevronRightIcon height={20} width={20} color={colors?.grey_7A7A7A} />
    </View>
  );
};
